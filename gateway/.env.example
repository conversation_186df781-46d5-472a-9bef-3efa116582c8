# KrakenD Gateway Configuration
# Copy this file to .env and update with your actual values

# Supabase Configuration
SUPABASE_PROJECT_ID=your-supabase-project-id
SUPABASE_URL=https://your-supabase-project-id.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Gateway Configuration
GATEWAY_PORT=5000
GATEWAY_HOST=0.0.0.0

# Voice Gateway Service
VOICE_GATEWAY_HOST=voice-gateway
VOICE_GATEWAY_PORT=8002

# Monitoring
TEMPO_HOST=tempo
TEMPO_GRPC_PORT=4317
TEMPO_HTTP_PORT=4318

# Metrics
METRICS_PORT=8090
PROMETHEUS_HOST=prometheus
PROMETHEUS_PORT=9090
