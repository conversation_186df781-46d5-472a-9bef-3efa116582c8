<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cortexa Voice Gateway Test UI</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .section h2 {
            margin-top: 0;
            color: #555;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #005a87;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
            font-weight: bold;
        }
        .endpoint-list {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
        }
        .endpoint-list h3 {
            margin-top: 0;
            color: #495057;
        }
        .endpoint {
            margin-bottom: 10px;
            padding: 8px;
            background-color: white;
            border-radius: 3px;
            border-left: 4px solid #007cba;
        }
        .method {
            font-weight: bold;
            color: #007cba;
        }
        .ws-status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Cortexa Voice Gateway Test UI</h1>
        
        <!-- Supabase Configuration -->
        <div class="section">
            <h2>1. Supabase Configuration</h2>
            <div class="form-group">
                <label for="supabaseUrl">Supabase URL:</label>
                <input type="url" id="supabaseUrl" placeholder="https://your-project.supabase.co">
            </div>
            <div class="form-group">
                <label for="supabaseKey">Supabase Anon Key:</label>
                <input type="text" id="supabaseKey" placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...">
            </div>
            <button onclick="initSupabase()">Initialize Supabase</button>
            <div id="supabaseStatus"></div>
        </div>

        <!-- Authentication -->
        <div class="section">
            <h2>2. Authentication</h2>
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" placeholder="<EMAIL>">
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" placeholder="password">
            </div>
            <button onclick="signUp()">Sign Up</button>
            <button onclick="signIn()">Sign In</button>
            <button onclick="signOut()">Sign Out</button>
            <div id="authStatus"></div>
            
            <div class="form-group" style="margin-top: 20px;">
                <label for="jwtToken">Current JWT Token:</label>
                <textarea id="jwtToken" readonly placeholder="JWT token will appear here after authentication"></textarea>
            </div>
        </div>

        <!-- API Testing -->
        <div class="section">
            <h2>3. API Testing</h2>
            <div class="form-group">
                <label for="gatewayUrl">Gateway URL:</label>
                <input type="url" id="gatewayUrl" value="http://localhost:8000" placeholder="http://localhost:8000">
            </div>
            
            <button onclick="testHealth()">Test Health</button>
            <button onclick="testVoiceHealth()">Test Voice Gateway Health</button>
            <button onclick="testDocs()">Open API Docs</button>
            <div id="apiResults"></div>

            <div class="endpoint-list">
                <h3>Available Endpoints:</h3>
                <div class="endpoint">
                    <span class="method">GET</span> /health - General gateway health
                </div>
                <div class="endpoint">
                    <span class="method">GET</span> /api/v1/voice-gateway/health - Voice gateway health
                </div>
                <div class="endpoint">
                    <span class="method">GET</span> /api/v1/voice-gateway/docs - API documentation
                </div>
                <div class="endpoint">
                    <span class="method">GET</span> /api/v1/voice-gateway/metrics - Prometheus metrics
                </div>
                <div class="endpoint">
                    <span class="method">WS</span> /api/v1/voice-gateway/call/ws/call/{call_id} - WebSocket (requires JWT)
                </div>
            </div>
        </div>

        <!-- WebSocket Testing -->
        <div class="section">
            <h2>4. WebSocket Testing</h2>
            <div class="form-group">
                <label for="callId">Call ID:</label>
                <input type="text" id="callId" value="test-call-123" placeholder="test-call-123">
            </div>
            
            <button onclick="connectWebSocket()">Connect WebSocket</button>
            <button onclick="disconnectWebSocket()">Disconnect</button>
            <button onclick="sendTestMessage()">Send Test Message</button>
            
            <div id="wsStatus" class="ws-status">
                <strong>Status:</strong> <span id="wsStatusText">Disconnected</span>
            </div>
            
            <div class="form-group" style="margin-top: 15px;">
                <label for="wsLog">WebSocket Log:</label>
                <div id="wsLog" class="log">WebSocket events will appear here...</div>
            </div>
        </div>
    </div>

    <!-- Supabase JS SDK -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <script>
        let supabase = null;
        let currentWebSocket = null;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const wsLog = document.getElementById('wsLog');
            wsLog.textContent += `[${timestamp}] ${message}\n`;
            wsLog.scrollTop = wsLog.scrollHeight;
        }

        function setStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<span class="${type}">${message}</span>`;
        }

        function initSupabase() {
            const url = document.getElementById('supabaseUrl').value;
            const key = document.getElementById('supabaseKey').value;
            
            if (!url || !key) {
                setStatus('supabaseStatus', 'Please enter both Supabase URL and Anon Key', 'error');
                return;
            }

            try {
                supabase = window.supabase.createClient(url, key);
                setStatus('supabaseStatus', 'Supabase initialized successfully!', 'success');
            } catch (error) {
                setStatus('supabaseStatus', `Error: ${error.message}`, 'error');
            }
        }

        async function signUp() {
            if (!supabase) {
                setStatus('authStatus', 'Please initialize Supabase first', 'error');
                return;
            }

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            try {
                const { data, error } = await supabase.auth.signUp({ email, password });
                if (error) throw error;
                
                setStatus('authStatus', 'Sign up successful! Check your email for confirmation.', 'success');
                updateJWTToken();
            } catch (error) {
                setStatus('authStatus', `Sign up error: ${error.message}`, 'error');
            }
        }

        async function signIn() {
            if (!supabase) {
                setStatus('authStatus', 'Please initialize Supabase first', 'error');
                return;
            }

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            try {
                const { data, error } = await supabase.auth.signInWithPassword({ email, password });
                if (error) throw error;
                
                setStatus('authStatus', 'Sign in successful!', 'success');
                updateJWTToken();
            } catch (error) {
                setStatus('authStatus', `Sign in error: ${error.message}`, 'error');
            }
        }

        async function signOut() {
            if (!supabase) return;

            try {
                await supabase.auth.signOut();
                setStatus('authStatus', 'Signed out successfully', 'info');
                document.getElementById('jwtToken').value = '';
            } catch (error) {
                setStatus('authStatus', `Sign out error: ${error.message}`, 'error');
            }
        }

        async function updateJWTToken() {
            if (!supabase) return;

            try {
                const { data: { session } } = await supabase.auth.getSession();
                if (session?.access_token) {
                    document.getElementById('jwtToken').value = session.access_token;
                }
            } catch (error) {
                console.error('Error getting JWT token:', error);
            }
        }

        async function testHealth() {
            const gatewayUrl = document.getElementById('gatewayUrl').value;
            
            try {
                const response = await fetch(`${gatewayUrl}/health`);
                const data = await response.json();
                setStatus('apiResults', `Health check successful: ${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                setStatus('apiResults', `Health check failed: ${error.message}`, 'error');
            }
        }

        async function testVoiceHealth() {
            const gatewayUrl = document.getElementById('gatewayUrl').value;
            
            try {
                const response = await fetch(`${gatewayUrl}/api/v1/voice-gateway/health`);
                const data = await response.json();
                setStatus('apiResults', `Voice gateway health check successful: ${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                setStatus('apiResults', `Voice gateway health check failed: ${error.message}`, 'error');
            }
        }

        function testDocs() {
            const gatewayUrl = document.getElementById('gatewayUrl').value;
            window.open(`${gatewayUrl}/api/v1/voice-gateway/docs`, '_blank');
        }

        function connectWebSocket() {
            const gatewayUrl = document.getElementById('gatewayUrl').value;
            const callId = document.getElementById('callId').value;
            const jwtToken = document.getElementById('jwtToken').value;

            if (!jwtToken) {
                log('Error: No JWT token available. Please sign in first.', 'error');
                return;
            }

            if (currentWebSocket) {
                currentWebSocket.close();
            }

            const wsUrl = gatewayUrl.replace('http://', 'ws://').replace('https://', 'wss://');
            const url = `${wsUrl}/api/v1/voice-gateway/call/ws/call/${callId}`;

            log(`Connecting to: ${url}`);
            
            currentWebSocket = new WebSocket(url, [], {
                headers: {
                    'Authorization': `Bearer ${jwtToken}`
                }
            });

            currentWebSocket.onopen = function(event) {
                log('WebSocket connected successfully!', 'success');
                document.getElementById('wsStatusText').textContent = 'Connected';
                document.getElementById('wsStatusText').className = 'success';
            };

            currentWebSocket.onmessage = function(event) {
                log(`Received: ${event.data}`);
            };

            currentWebSocket.onclose = function(event) {
                log(`WebSocket closed. Code: ${event.code}, Reason: ${event.reason}`);
                document.getElementById('wsStatusText').textContent = 'Disconnected';
                document.getElementById('wsStatusText').className = 'error';
            };

            currentWebSocket.onerror = function(error) {
                log(`WebSocket error: ${error}`, 'error');
                document.getElementById('wsStatusText').textContent = 'Error';
                document.getElementById('wsStatusText').className = 'error';
            };
        }

        function disconnectWebSocket() {
            if (currentWebSocket) {
                currentWebSocket.close();
                currentWebSocket = null;
                log('WebSocket disconnected by user');
            }
        }

        function sendTestMessage() {
            if (!currentWebSocket || currentWebSocket.readyState !== WebSocket.OPEN) {
                log('Error: WebSocket is not connected', 'error');
                return;
            }

            const testMessage = JSON.stringify({
                type: 'test',
                message: 'Hello from test UI!',
                timestamp: new Date().toISOString()
            });

            currentWebSocket.send(testMessage);
            log(`Sent: ${testMessage}`);
        }

        // Initialize on page load
        window.onload = function() {
            log('Test UI loaded. Configure Supabase to get started.');
        };
    </script>
</body>
</html>
