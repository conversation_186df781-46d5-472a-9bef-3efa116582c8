{"version": 3, "timeout": "3000ms", "cache_ttl": "300s", "output_encoding": "json", "name": "cortexa-gateway", "port": 5000, "extra_config": {"telemetry/opentelemetry": {"service_name": "cortexa-gateway", "service_version": "1.0.0", "skip_paths": ["/health", "/metrics"], "exporters": {"otlp": [{"name": "tempo", "host": "tempo", "port": 4317, "use_http": false, "disable_metrics": false, "disable_traces": false}]}, "layers": {"global": {"disable_metrics": false, "disable_traces": false, "disable_propagation": false}, "proxy": {"disable_metrics": false, "disable_traces": false}, "pipe": {"disable_metrics": false, "disable_traces": false}}}, "security/cors": {"allow_origins": ["*"], "allow_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "allow_headers": ["*"], "expose_headers": ["Content-Length"], "max_age": "12h", "allow_credentials": true}, "github_com/devopsfaith/krakend-gologging": {"level": "INFO", "prefix": "[CORTEXA-GATEWAY]", "syslog": false, "stdout": true, "format": "default"}, "github_com/devopsfaith/krakend-metrics": {"collection_time": "30s", "listen_address": ":8090"}}, "endpoints": [{"endpoint": "/health", "method": "GET", "backend": [{"url_pattern": "/api/v1/health", "method": "GET", "host": ["http://voice-gateway:8002"], "extra_config": {"backend/http": {"return_error_details": "voice-gateway"}}}], "extra_config": {}}, {"endpoint": "/api/v1/voice-gateway/health", "method": "GET", "backend": [{"url_pattern": "/api/v1/health", "method": "GET", "host": ["http://voice-gateway:8002"], "extra_config": {"backend/http": {"return_error_details": "voice-gateway"}}}], "extra_config": {}}, {"endpoint": "/api/v1/voice-gateway/call/ws/call/{call_id}", "method": "GET", "headers_to_pass": ["Authorization", "Upgrade", "Connection", "Sec-WebSocket-Key", "Sec-WebSocket-Version", "Sec-WebSocket-Protocol", "Sec-WebSocket-Extensions"], "backend": [{"url_pattern": "/api/v1/call/ws/call/{call_id}", "method": "GET", "host": ["http://voice-gateway:8002"], "extra_config": {"backend/http": {"return_error_details": "voice-gateway"}}}], "extra_config": {"proxy": {"static": {"strategy": "always", "data": {}}}, "websocket": {"connect_event": true, "disconnect_event": true, "read_buffer_size": 1024, "write_buffer_size": 1024, "message_buffer_size": 256, "max_message_size": 512, "write_wait": 10, "pong_wait": 60, "ping_period": 54, "max_retries": 0, "backoff_strategy": "exponential"}, "auth/validator": {"alg": "HS256", "jwk_url": "https://{{.SUPABASE_PROJECT_ID}}.supabase.co/auth/v1/jwks", "issuer": "https://{{.SUPABASE_PROJECT_ID}}.supabase.co/auth/v1", "audience": ["authenticated"], "roles_key": "role", "roles": ["authenticated"], "disable_jwk_security": false, "operation_debug": true}}}, {"endpoint": "/api/v1/voice-gateway/docs", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/v1/docs", "method": "GET", "encoding": "no-op", "host": ["http://voice-gateway:8002"], "extra_config": {"backend/http": {"return_error_details": "voice-gateway"}}}], "extra_config": {}}, {"endpoint": "/api/v1/voice-gateway/openapi.json", "method": "GET", "output_encoding": "json", "backend": [{"url_pattern": "/api/v1/openapi.json", "method": "GET", "encoding": "json", "host": ["http://voice-gateway:8002"], "extra_config": {"backend/http": {"return_error_details": "voice-gateway"}}}], "extra_config": {}}, {"endpoint": "/api/v1/voice-gateway/metrics", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/metrics", "method": "GET", "encoding": "no-op", "host": ["http://voice-gateway:8002"], "extra_config": {"backend/http": {"return_error_details": "voice-gateway"}}}], "extra_config": {}}]}