# Cortexa KrakenD Gateway

This directory contains the KrakenD API Gateway configuration for the Cortexa voice translation platform.

## Quick Start

1. **Configure Environment Variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your actual Supabase configuration
   ```

2. **Start Services:**
   ```bash
   docker-compose up -d
   ```

3. **Test the Gateway:**
   - Health Check: http://localhost:8000/health
   - Test UI: http://localhost:3001
   - API Docs: http://localhost:8000/api/v1/voice-gateway/docs

## Environment Variables

Only the following variables are required:

- `SUPABASE_PROJECT_ID` - Your Supabase project ID (required for JWT validation)
- `SUPABASE_URL` - Your Supabase URL (optional, only for test UI)
- `SUPABASE_ANON_KEY` - Your Supabase anon key (optional, only for test UI)

## Available Endpoints

### General Gateway
- `GET /health` - Gateway health check

### Voice Gateway Service (`/api/v1/voice-gateway/`)
- `GET /health` - Voice gateway health check
- `WS /call/ws/call/{call_id}` - WebSocket for voice translation (requires JWT)
- `GET /docs` - API documentation
- `GET /openapi.json` - OpenAPI specification
- `GET /metrics` - Prometheus metrics

## Testing with the UI

1. Open http://localhost:3001
2. Configure your Supabase credentials
3. Sign up/sign in to get a JWT token
4. Test API endpoints and WebSocket connections

## WebSocket Connection Example

```javascript
const ws = new WebSocket(
  'ws://localhost:8000/api/v1/voice-gateway/call/ws/call/test-call-id',
  [],
  {
    headers: {
      'Authorization': 'Bearer YOUR_JWT_TOKEN'
    }
  }
);
```

## Monitoring

- **Gateway Metrics**: http://localhost:8091/__stats
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin)
- **Tempo**: http://localhost:3200

## Architecture

```
Client → KrakenD Gateway (port 8000) → Voice Gateway Service (port 8002)
                                    → Monitoring Stack (Tempo, Prometheus)
```

## Configuration Details

- **JWT Validation**: Uses Supabase JWT tokens
- **Content Types**: Handles JSON, HTML, and Prometheus metrics
- **WebSocket Support**: Proper header forwarding and connection management
- **Observability**: OpenTelemetry tracing to Tempo, metrics to Prometheus
- **CORS**: Configured for development (update for production)

## Production Considerations

1. Update CORS origins for your domain
2. Configure proper SSL/TLS termination
3. Set up rate limiting
4. Configure proper JWT security settings
5. Set up monitoring alerts
