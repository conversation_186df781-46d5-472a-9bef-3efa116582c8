from fastapi import APIRouter, Depends

from src.core.config import settings
from src.core.context import ApplicationContext, get_app_context
from src.core.dependencies import get_s2st_processor
from src.pipeline.s2st import S2STProcessor

router = APIRouter()


@router.get("/health")
async def health_check(
    s2st_processor: S2STProcessor | None = Depends(get_s2st_processor),
    app_context: ApplicationContext = Depends(get_app_context)
):
    """Health check endpoint with S2ST processor status."""
    connection_count = await app_context.connection_state_manager.get_connection_count()

    # Get S2ST processor health status
    s2st_status = None
    ready_for_calls = False

    if s2st_processor:
        s2st_status = await s2st_processor.get_health_status()
        ready_for_calls = s2st_processor.is_initialized

    return {
        "status": "healthy" if ready_for_calls else "initializing",
        "ready": ready_for_calls,
        "service": settings.service_name,
        "version": "0.1.0",
        "active_connections": connection_count,
        "max_connections": settings.ws_max_connections,
        "s2st_processor": s2st_status,
    }
