from pydantic import Field
from cortexacommon.events.schemas import BaseEvent


class CallStartedEvent(BaseEvent):
    """Event published when a voice call starts."""
    
    event_type: str = Field(default="call.started", description="Event type")
    call_id: str = Field(description="Unique call identifier")


class CallEndedEvent(BaseEvent):
    """Event published when a voice call ends."""
    
    event_type: str = Field(default="call.ended", description="Event type")
    call_id: str = Field(description="Unique call identifier")
    duration_seconds: float = Field(description="Call duration in seconds")