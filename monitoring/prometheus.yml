global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  # KrakenD metrics
  - job_name: "krakend"
    static_configs:
      - targets: ["krakend:8090"]
    metrics_path: /metrics

  # Voice Gateway metrics
  - job_name: "voice-gateway"
    static_configs:
      - targets: ["voice-gateway:8002"]
    metrics_path: /metrics

  # Add other services as they become available
  # - job_name: "call-data-service"
  #   static_configs:
  #     - targets: ["call-data-service:8003"]
  #   metrics_path: /metrics
