"""
Gateway Authentication Module

This module handles authentication for Cortexa services when running behind
an Apache APISIX gateway with JWT authentication.

The gateway validates JWT tokens and forwards user identity information via headers.
This module parses and validates that information to establish user context.
"""

import json
import base64
import logging
from typing import Any
from datetime import datetime, timezone
from dataclasses import dataclass

logger = logging.getLogger(__name__)


class GatewayAuthenticationError(Exception):
    """Exception raised for authentication errors."""
    pass


@dataclass
class AuthenticatedUser:
    """
    Represents an authenticated user with basic identity information.

    This data is extracted from headers provided by the APISIX gateway
    after successful JWT validation.
    """

    # Core identity
    user_id: str
    email: str

    # Metadata
    user_metadata: dict[str, Any]
    app_metadata: dict[str, Any]

    # Token information
    token_kid: str
    token_issuer: str
    token_audience: str
    token_expires_at: int
    token_issued_at: int

    # Validation timestamp
    validated_at: str

    @property
    def is_token_expired(self) -> bool:
        """Check if the JWT token has expired."""
        current_timestamp = int(datetime.now(timezone.utc).timestamp())
        return current_timestamp >= self.token_expires_at


class GatewayAuthenticator:
    """
    Parses authentication information from gateway headers.
    
    The APISIX gateway validates JWT tokens and forwards user context
    via standardized headers. This class extracts and validates that information.
    """
    
    # Header names used by the JWT authorizer plugin
    USER_CONTEXT_HEADER = "X-User-Context"
    USER_ID_HEADER = "X-User-ID"
    USER_EMAIL_HEADER = "X-User-Email"
    
    def __init__(self):
        """Initialize the gateway auth parser."""
        self.logger = logging.getLogger(f"{__name__}.GatewayAuthParser")
    
    def parse_user_from_headers(self, headers: dict[str, str]) -> AuthenticatedUser:
        """Parse authenticated user information from gateway headers."""
        # Check if user context header is present
        user_context_b64 = headers.get(self.USER_CONTEXT_HEADER)
        if not user_context_b64:
            self.logger.debug("No user context header found - request not authenticated")
            raise GatewayAuthenticationError("Request not authenticated")
        
        user = self._extract_user_context(user_context_b64)
            
        # Validate token expiration
        if user.is_token_expired:
            self.logger.warning(f"Token expired for user {user.user_id}")
            raise GatewayAuthenticationError("Token has expired")
        
        self.logger.info(f"Successfully parsed user context for {user.user_id}")
        return user

    def _extract_user_context(self, user_context_b64: str) -> AuthenticatedUser:
        """Extract and decode user context from headers."""
        try:
            user_context_json = base64.b64decode(user_context_b64).decode('utf-8')
            user_context = json.loads(user_context_json)
        except (ValueError, json.JSONDecodeError) as e:
            self.logger.error(f"Failed to decode user context header: {e}")
            raise GatewayAuthenticationError("Invalid user context header format")
        
        # Extract user information
        user_info = user_context.get('user', {})
        token_info = user_context.get('token', {})

        # Create authenticated user
        return AuthenticatedUser(
            user_id=user_info.get('id', ''),
            email=user_info.get('email', ''),
            user_metadata=user_info.get('metadata', {}),
            app_metadata=user_info.get('app_metadata', {}),
            token_kid=token_info.get('kid', ''),
            token_issuer=token_info.get('iss', ''),
            token_audience=token_info.get('aud', ''),
            token_expires_at=token_info.get('exp', 0),
            token_issued_at=token_info.get('iat', 0),
            validated_at=user_context.get('validated_at', '')
        )
